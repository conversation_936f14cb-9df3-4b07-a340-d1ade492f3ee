import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";

import { AuthProvider } from "./components/auth/AuthProvider";
import { RequireAuth } from "./components/auth/RequireAuth";
import AppLayout from "./components/layout/AppLayout";

import Login from "./pages/Login";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";

import RecruiterDashboard from "./pages/dashboard/RecruiterDashboard";
import ManagerDashboard from "./pages/dashboard/ManagerDashboard";
import InterviewerDashboard from "./pages/dashboard/InterviewerDashboard";
import HRDashboard from "./pages/dashboard/HRDashboard";

import Jobs from "./pages/Jobs";
import Candidates from "./pages/Candidates";
import Interviews from "./pages/Interviews";
import ScheduleInterview from "@/pages/ScheduleInterview";
import Offers from "./pages/Offers";
import Reports from "./pages/Reports";
import Settings from "./pages/Settings";

import NewJob from "./pages/NewJob";
import Interviewers from "./pages/Interviewers";

import FeedbackView from "./pages/FeedbackView";

import { JobCandidatesPage } from "@/components/jobs/JobCandidatesPage";
import { msalConfig } from "./config/microsoft.config.ts";
import { PublicClientApplication } from "@azure/msal-browser";

export const msalInstance = new PublicClientApplication(msalConfig);

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Router>
        <AuthProvider>
          <AppLayout>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/login" element={<Login />} />

              {/* Role-specific dashboards */}
              <Route
                path="/dashboard/recruiter"
                element={
                  <RequireAuth allowedRoles={["recruiter"]}>
                    <RecruiterDashboard />
                  </RequireAuth>
                }
              />
              <Route
                path="/dashboard/manager"
                element={
                  <RequireAuth allowedRoles={["manager"]}>
                    <ManagerDashboard />
                  </RequireAuth>
                }
              />
              <Route
                path="/dashboard/interviewer"
                element={
                  <RequireAuth allowedRoles={["interviewer"]}>
                    <InterviewerDashboard />
                  </RequireAuth>
                }
              />
              <Route
                path="/dashboard/hr"
                element={
                  <RequireAuth allowedRoles={["hr"]}>
                    <HRDashboard />
                  </RequireAuth>
                }
              />

              {/* Main feature pages */}
              <Route
                path="/interviewers"
                element={
                  <RequireAuth allowedRoles={["recruiter", "manager"]}>
                    <Interviewers />
                  </RequireAuth>
                }
              />
              <Route
                path="/jobs"
                element={
                  <RequireAuth allowedRoles={["recruiter", "manager", "hr"]}>
                    <Jobs />
                  </RequireAuth>
                }
              />
              <Route
                path="/candidates"
                element={
                  <RequireAuth allowedRoles={["recruiter", "manager", "interviewer", "hr"]}>
                    <Candidates />
                  </RequireAuth>
                }
              />
              <Route
                path="/interviews"
                element={
                  <RequireAuth allowedRoles={["recruiter", "manager", "interviewer"]}>
                    <Interviews />
                  </RequireAuth>
                }
              />
              <Route
                path="/offers"
                element={
                  <RequireAuth allowedRoles={["recruiter", "manager", "hr"]}>
                    <Offers />
                  </RequireAuth>
                }
              />
              <Route
                path="/reports"
                element={
                  <RequireAuth allowedRoles={["recruiter", "manager", "hr"]}>
                    <Reports />
                  </RequireAuth>
                }
              />
              <Route
                path="/settings"
                element={
                  <RequireAuth>
                    <Settings />
                  </RequireAuth>
                }
              />

              {/* Catch-all route */}
              <Route
                path="/jobs/new"
                element={
                  <RequireAuth allowedRoles={["recruiter", "manager", "hr"]}>
                    <NewJob />
                  </RequireAuth>
                }
              />
              <Route path="*" element={<NotFound />} />
              <Route path="/jobs/:jobId/candidates" element={<JobCandidatesPage />} />
              <Route path="/interviews/feedback/:interviewId" element={<FeedbackView />} />
            </Routes>
          </AppLayout>{" "}
          <Toaster />
        </AuthProvider>
      </Router>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
