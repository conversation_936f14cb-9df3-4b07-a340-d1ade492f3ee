import { useState, useEffect } from "react";
import { Mail, Phone, MapPin, Edit, Save, Trash2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import {
  She<PERSON>,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  useInterviewers,
  BackendInterviewerDetail,
  InterviewerDetail,
} from "@/hooks/useInterviewers";
import { toast } from "@/components/ui/sonner";

interface InterviewerDrawerProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  interviewer: InterviewerDetail | null;
  onUpdate: (updatedInterviewer: InterviewerDetail) => void;
  onDelete?: (interviewer: InterviewerDetail) => void;
}

export const InterviewerDrawer = ({
  isOpen,
  onOpenChange,
  interviewer,
  onUpdate,
  onDelete,
}: InterviewerDrawerProps) => {
  const { updateInterviewer, loading } = useInterviewers();

  const [activeTab, setActiveTab] = useState("job");
  const [isEditing, setIsEditing] = useState(false);
  const getDefaultInterviewer = (data: InterviewerDetail | null): InterviewerDetail | null => {
    if (!data) return null;
    return {
      ...data,
      education: Array.isArray(data.education) ? data.education : [],
      workHistory: Array.isArray(data.workHistory) ? data.workHistory : [],
      availability: Array.isArray(data.availability) ? data.availability : [],
      expertise: Array.isArray(data.expertise) ? data.expertise : [],
      interviewTypes: Array.isArray(data.interviewTypes) ? data.interviewTypes : [],
    };
  };

  const [editedData, setEditedData] = useState<InterviewerDetail | null>(
    getDefaultInterviewer(interviewer)
  );

  useEffect(() => {
    if (interviewer) {
      console.log("InterviewerDrawer: interviewer prop changed", interviewer);
      setEditedData(getDefaultInterviewer(interviewer));
    }
  }, [interviewer]);

  // Effect to reset isEditing mode when the drawer is closed
  useEffect(() => {
    if (!isOpen) {
      setIsEditing(false);
    }
  }, [isOpen]);

  if (!interviewer) return null;

  // Defensive: ensure arrays are always present
  interviewer = {
    ...interviewer,
    education: Array.isArray(interviewer?.education) ? interviewer.education : [],
    workHistory: Array.isArray(interviewer?.workHistory) ? interviewer.workHistory : [],
    availability: Array.isArray(interviewer?.availability) ? interviewer.availability : [],
    expertise: Array.isArray(interviewer?.expertise) ? interviewer.expertise : [],
    interviewTypes: Array.isArray(interviewer?.interviewTypes) ? interviewer.interviewTypes : [],
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map(n => n[0])
      .join("")
      .toUpperCase();
  };

  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "short",
      day: "numeric",
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const handleInputChange = (
    field: string,
    value: string | number | boolean | string[] | { day: string; slots: string[] }[]
  ) => {
    setEditedData(prev => {
      const newState = {
        ...prev,
        [field]: value,
      };
      if (field === "availability") {
        console.log(
          "InterviewerDrawer: editedData.availability changed to:",
          newState.availability
        );
      }
      return newState;
    });
  };

  const handleSave = async () => {
    if (!interviewer) return;

    // If the email hasn't changed, don't include it in the update
    const backendFormattedData: Partial<BackendInterviewerDetail> = {
      full_name: editedData.name || "",
      // Only include email if it has changed
      ...(editedData.email !== interviewer.email && { email: editedData.email }),
      phone: editedData.phone || "",
      job_title: editedData.title || "",
      department: editedData.department || "",
      location: editedData.location || "",
      employee_id: editedData.employeeId || "",
      employment_type: editedData.employmentType || "full-time",
      designation: editedData.designation || "",
      join_date: editedData.joinDate || new Date().toISOString().split("T")[0],
      bio: editedData.bio || "",
      areas_of_expertise: Array.isArray(editedData.expertise) ? editedData.expertise : [],

      // Ensure education fields are strings
      education_degree: editedData.education[0]?.degree || "",
      education_institution: editedData.education[0]?.institution || "",
      education_year: editedData.education[0]?.year || "",

      // Ensure work history fields are strings
      work_history_position: editedData.workHistory[0]?.position || "",
      work_history_company: editedData.workHistory[0]?.company || "",
      work_history_duration: editedData.workHistory[0]?.duration || "",

      is_available: editedData.isAvailable,
      interview_types: editedData.interviewTypes || [],
      max_interviews_per_day: editedData.maxInterviewsPerDay,
      preferred_time_slots: editedData.availability.reduce((acc, item) => {
        acc[item.day.toLowerCase()] = item.slots;
        return acc;
      }, {} as Record<string, string[]>),
    };
    console.log(
      "InterviewerDrawer: Sending to backend, preferred_time_slots:",
      backendFormattedData.preferred_time_slots
    );

    try {
      const result = await updateInterviewer(interviewer.id, backendFormattedData);
      if (result.success && result.interviewer) {
        onUpdate(result.interviewer);

        setTimeout(() => {
          setIsEditing(false);
          toast.success("Interviewer Updated", {
            description: `${result.interviewer.name} has been updated.`,
          });
        }, 0);
      }
    } catch (error) {
      console.error("Error saving interviewer:", error);
      toast.error("Update Failed", {
        description: error instanceof Error ? error.message : "Failed to update interviewer",
      });
    }
  };

  const handleExpertiseChange = (value: string) => {
    const expertiseArray = value
      .split(",")
      .map(s => s.trim())
      .filter(s => s.length > 0);
    handleInputChange("expertise", expertiseArray);
  };

  const renderEditableField = (
    label: string,
    field: keyof Pick<
      InterviewerDetail,
      {
        [K in keyof InterviewerDetail]: InterviewerDetail[K] extends string ? K : never;
      }[keyof InterviewerDetail]
    >
  ) => {
    return (
      <div>
        <p className="text-sm text-muted-foreground">{label}</p>
        {isEditing ? (
          <Input
            value={editedData[field]}
            onChange={e => handleInputChange(field, e.target.value)}
            className="mt-1"
          />
        ) : (
          <p className="font-medium">{interviewer[field]}</p>
        )}
      </div>
    );
  };

  const handleWorkHistoryChange = (index: number, field: string, value: string) => {
    setEditedData(prev => ({
      ...prev,
      workHistory: prev.workHistory.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      ),
    }));
  };

  const handleEducationChange = (index: number, field: string, value: string) => {
    setEditedData(prev => ({
      ...prev,
      education: prev.education.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      ),
    }));
  };

  const renderEducationFields = () => {
    const educationArr = Array.isArray(editedData?.education) ? editedData.education : [];
    const educationEntry = educationArr[0] || { degree: "", institution: "", year: "" };
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <p className="text-sm text-muted-foreground">Degree</p>
          {isEditing ? (
            <Input
              value={educationEntry.degree}
              onChange={e => handleEducationChange(0, "degree", e.target.value)}
              className="mt-1"
            />
          ) : (
            <p className="font-medium">
              {(Array.isArray(interviewer.education) && interviewer.education[0]?.degree) || "N/A"}
            </p>
          )}
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Institution</p>
          {isEditing ? (
            <Input
              value={educationEntry.institution}
              onChange={e => handleEducationChange(0, "institution", e.target.value)}
              className="mt-1"
            />
          ) : (
            <p className="font-medium">
              {(Array.isArray(interviewer.education) && interviewer.education[0]?.institution) ||
                "N/A"}
            </p>
          )}
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Year</p>
          {isEditing ? (
            <Input
              value={educationEntry.year}
              onChange={e => handleEducationChange(0, "year", e.target.value)}
              className="mt-1"
            />
          ) : (
            <p className="font-medium">
              {(Array.isArray(interviewer.education) && interviewer.education[0]?.year) || "N/A"}
            </p>
          )}
        </div>
      </div>
    );
  };

  const renderWorkHistoryFields = () => {
    const workHistoryArr = Array.isArray(editedData?.workHistory) ? editedData.workHistory : [];
    const workHistoryEntry = workHistoryArr[0] || {
      position: "",
      company: "",
      duration: "",
    };
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <p className="text-sm text-muted-foreground">Position</p>
          {isEditing ? (
            <Input
              value={workHistoryEntry.position}
              onChange={e => handleWorkHistoryChange(0, "position", e.target.value)}
              className="mt-1"
            />
          ) : (
            <p className="font-medium">
              {(Array.isArray(interviewer.workHistory) && interviewer.workHistory[0]?.position) ||
                "N/A"}
            </p>
          )}
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Company</p>
          {isEditing ? (
            <Input
              value={workHistoryEntry.company}
              onChange={e => handleWorkHistoryChange(0, "company", e.target.value)}
              className="mt-1"
            />
          ) : (
            <p className="font-medium">
              {(Array.isArray(interviewer.workHistory) && interviewer.workHistory[0]?.company) ||
                "N/A"}
            </p>
          )}
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Duration</p>
          {isEditing ? (
            <Input
              value={workHistoryEntry.duration}
              onChange={e => handleWorkHistoryChange(0, "duration", e.target.value)}
              className="mt-1"
            />
          ) : (
            <p className="font-medium">
              {(Array.isArray(interviewer.workHistory) && interviewer.workHistory[0]?.duration) ||
                "N/A"}
            </p>
          )}
        </div>
      </div>
    );
  };

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent
        side="right"
        className="min-w-[70vw] p-0 overflow-y-auto"
        aria-describedby="interviewer-drawer-description"
      >
        <SheetDescription id="interviewer-drawer-description" className="sr-only">
          Interviewer profile and details
        </SheetDescription>
        <div className="h-full overflow-y-auto">
          <SheetHeader className="px-6 pt-6 pb-2">
            <div className="flex items-start justify-between w-full mb-6">
              <h2 className="text-lg font-semibold">Interviewer Details</h2>
              <div className="flex gap-2">
                {onDelete && (
                  <Button
                    variant="destructive"
                    size="sm"
                    className="gap-2"
                    onClick={() => onDelete(interviewer)}
                    disabled={loading}
                  >
                    <Trash2 className="h-4 w-4" />
                    Delete
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-2"
                  onClick={isEditing ? handleSave : () => setIsEditing(true)}
                  disabled={loading}
                >
                  {isEditing ? <Save className="h-4 w-4" /> : <Edit className="h-4 w-4" />}
                  {isEditing ? "Save Changes" : "Edit Details"}
                </Button>
              </div>
            </div>

            <div className="flex items-start flex-col md:flex-row gap-6">
              {/* Left column - Basic info */}
              <div className="w-full md:w-1/3 pr-0 md:pr-6 border-0 md:border-r border-gray-200">
                <div className="flex flex-col items-center text-center mb-4">
                  <Avatar className="h-20 w-20 mb-2 border-2 border-primary/10">
                    <AvatarFallback className="text-xl bg-primary/5 text-primary">
                      {getInitials(interviewer.name)}
                    </AvatarFallback>
                  </Avatar>

                  <SheetTitle className="text-2xl mb-1">{interviewer.name}</SheetTitle>
                  <p className="text-gray-600 mb-1">{interviewer.title}</p>
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 mb-3">
                    {interviewer.department}
                  </Badge>

                  <div className="text-sm text-gray-600 flex flex-col gap-2 w-full">
                    <div className="flex items-center justify-center gap-2">
                      <Mail className="h-4 w-4 text-gray-500" />
                      <span>{interviewer.email}</span>
                    </div>
                    <div className="flex items-center justify-center gap-2">
                      <Phone className="h-4 w-4 text-gray-500" />
                      <span>{interviewer.phone}</span>
                    </div>
                    <div className="flex items-center justify-center gap-2">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <span>{interviewer.location}</span>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide mb-3">
                    Interviewer Info
                  </h3>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Interviews</span>
                      <span className="font-medium">{interviewer.totalInterviews}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Upcoming Interviews</span>
                      <span className="font-medium">{interviewer.upcomingInterviews}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Joined</span>
                      <span className="font-medium">{formatDate(interviewer.joinDate)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right column - Detailed info with tabs */}
              <div className="w-full md:w-2/3 pl-0 md:pl-6 mt-6 md:mt-0">
                <Tabs
                  defaultValue="job"
                  className="w-full"
                  value={activeTab}
                  onValueChange={setActiveTab}
                >
                  <TabsList className="w-full justify-start border-b rounded-none bg-transparent h-auto pb-0 mb-6 gap-4">
                    <TabsTrigger
                      value="job"
                      className="text-sm rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent pb-2"
                    >
                      Job
                    </TabsTrigger>
                    <TabsTrigger
                      value="personal"
                      className="text-sm rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent pb-2"
                    >
                      Personal
                    </TabsTrigger>
                    <TabsTrigger
                      value="employment"
                      className="text-sm rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent pb-2"
                    >
                      Employment & Education
                    </TabsTrigger>
                    <TabsTrigger
                      value="availability"
                      className="text-sm rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent pb-2"
                    >
                      Availability
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="job" className="space-y-4 mt-0">
                    <Card>
                      <CardContent className="p-6 space-y-4">
                        {renderEditableField("Job Title", "title")}
                        {renderEditableField("Department", "department")}
                        {renderEditableField("Location", "location")}
                        <div>
                          <p className="text-sm text-muted-foreground">Areas of Expertise</p>
                          {isEditing ? (
                            <Textarea
                              value={editedData.expertise.join(", ")}
                              onChange={e => handleExpertiseChange(e.target.value)}
                              className="mt-1"
                            />
                          ) : (
                            <div className="flex flex-wrap gap-2 mt-1">
                              {(Array.isArray(interviewer.expertise)
                                ? interviewer.expertise
                                : []
                              ).map((skill, index) => (
                                <Badge key={index} variant="secondary">
                                  {skill}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Bio</p>
                          {isEditing ? (
                            <Textarea
                              value={editedData.bio || ""}
                              onChange={e => handleInputChange("bio", e.target.value)}
                              className="mt-1"
                            />
                          ) : (
                            <p className="font-medium">{interviewer.bio}</p>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="personal" className="space-y-4 mt-0">
                    <Card>
                      <CardContent className="p-6 space-y-4">
                        {renderEditableField("Full Name", "name")}
                        {renderEditableField("Email", "email")}
                        {renderEditableField("Phone", "phone")}
                        {renderEditableField("Join Date", "joinDate")}
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="employment" className="space-y-4 mt-0">
                    <Card>
                      <CardContent className="p-6 space-y-4">
                        {renderEditableField("Employee ID", "employeeId")}
                        {renderEditableField("Employment Type", "employmentType")}
                        {renderEditableField("Designation", "designation")}

                        <h4 className="text-md font-semibold text-gray-700 mt-4 mb-2">Education</h4>
                        {renderEducationFields()}

                        <h4 className="text-md font-semibold text-gray-700 mt-4 mb-2">
                          Work History
                        </h4>
                        {renderWorkHistoryFields()}
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="availability" className="space-y-4 mt-0">
                    <Card>
                      <CardContent className="p-6 space-y-4">
                        {/* Is Available */}
                        <div>
                          <p className="text-sm text-muted-foreground">Is Available</p>
                          {isEditing ? (
                            <input
                              type="checkbox"
                              checked={editedData.isAvailable}
                              onChange={e => handleInputChange("isAvailable", e.target.checked)}
                              className="mt-1"
                            />
                          ) : (
                            <p className="font-medium">{interviewer.isAvailable ? "Yes" : "No"}</p>
                          )}
                        </div>

                        {/* Max Interviews Per Day */}
                        <div>
                          <p className="text-sm text-muted-foreground">Max Interviews Per Day</p>
                          {isEditing ? (
                            <Input
                              type="number"
                              value={editedData.maxInterviewsPerDay}
                              onChange={e =>
                                handleInputChange("maxInterviewsPerDay", parseInt(e.target.value))
                              }
                              className="mt-1"
                            />
                          ) : (
                            <p className="font-medium">{interviewer.maxInterviewsPerDay}</p>
                          )}
                        </div>

                        {/* Interview Types */}
                        <div>
                          <p className="text-sm text-muted-foreground">Interview Types</p>
                          {isEditing ? (
                            <Textarea
                              value={editedData.interviewTypes.join(", ")}
                              onChange={e =>
                                handleInputChange(
                                  "interviewTypes",
                                  e.target.value.split(",").map(s => s.trim())
                                )
                              }
                              className="mt-1"
                            />
                          ) : (
                            <div className="flex flex-wrap gap-2 mt-1">
                              {(Array.isArray(interviewer.interviewTypes)
                                ? interviewer.interviewTypes
                                : []
                              ).map((type, index) => (
                                <Badge key={index} variant="secondary">
                                  {type}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>

                        {/* Preferred Time Slots */}
                        <div>
                          <p className="text-sm text-muted-foreground">Preferred Time Slots</p>
                          {isEditing ? (
                            <Textarea
                              value={editedData.availability
                                .map(
                                  (item: { day: string; slots: string[] }) =>
                                    `${item.day}: ${
                                      Array.isArray(item.slots) ? item.slots.join(", ") : ""
                                    }`
                                )
                                .join("\n")}
                              onChange={e => {
                                const newTimeSlots: { day: string; slots: string[] }[] = [];
                                e.target.value.split("\n").forEach(line => {
                                  const [day, ...slots] = line.split(":").map(s => s.trim());
                                  if (day && slots.length > 0) {
                                    newTimeSlots.push({
                                      day,
                                      slots: slots[0].split(",").map(s => s.trim()),
                                    });
                                  }
                                });
                                handleInputChange("availability", newTimeSlots);
                              }}
                              className="mt-1"
                              placeholder="e.g., Monday: 9:00 AM, 2:00 PM\nTuesday: 10:00 AM"
                            />
                          ) : (
                            <div className="space-y-1 mt-1">
                              {Array.isArray(interviewer.availability) &&
                              interviewer.availability.length > 0 ? (
                                interviewer.availability.map((item, index) => (
                                  <p key={index} className="font-medium">
                                    {item.day}:{" "}
                                    {Array.isArray(item.slots) ? item.slots.join(", ") : ""}
                                  </p>
                                ))
                              ) : (
                                <p className="text-muted-foreground">No preferred time slots.</p>
                              )}
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </SheetHeader>
        </div>
      </SheetContent>
    </Sheet>
  );
};
