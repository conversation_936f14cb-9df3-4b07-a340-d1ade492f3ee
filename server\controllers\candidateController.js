import { logger } from "../utils/logger.js";
import { responseHandler } from "../utils/responseHandler.js";
import { candidateService } from "../services/candidateService.js";
import { messages } from "../utils/messages.utils.js";
import FormData from "form-data";

export const candidateController = {
  async getCandidates(req, res) {
    try {
      let { page, limit, ...rest } = req.query;
      page = parseInt(page) || 1;
      limit = parseInt(limit) || 10;
      let filters = {
        ...rest,
      };

      const result = await candidateService.getCandidates(page, limit, filters);
      logger.info("Candidates retrieved successfully");
      return responseHandler.success(res, result, messages.GET_CANDIDATES);
    } catch (err) {
      logger.error("Failed to get candidates:", err);
      return responseHandler.error(res, "Failed to retrieve candidates");
    }
  },

  async getCandidateById(req, res) {
    try {
      const result = await candidateService.getCandidateById(req.params.id);

      if (!result.success) {
        logger.info(`Candidate not found: ${req.params.id}`);
        return responseHandler.notFound(res, "Candidate not found");
      }

      logger.info(`Candidate retrieved: ${req.params.id}`);
      return responseHandler.success(res, result.candidate, messages.GET_CANDIDATE);
    } catch (err) {
      logger.error("Failed to get candidate:", err);
      return responseHandler.error(res, "Failed to retrieve candidate");
    }
  },

  async bulkUploadCandidates(req, res) {
    try {
      const { job_id, source, oneDriveFolderId, can_status } = req.body;

      const payload = {
        job_id,
        source,
        folder_id: oneDriveFolderId,
        can_status,
        created_by: req.user.id,
      };
      const result = await candidateService.bulkUploadCandidates(payload);

      if (!result.success) {
        logger.info(`Bulk upload failed: ${result.message}`);
        return responseHandler.error(res, result.message, result.status || 400);
      }

      logger.info("Bulk upload successful");
      return responseHandler.success(res, result, "Bulk upload successful");
    } catch (error) {
      logger.error("Failed to bulk upload candidates:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to bulk upload candidates",
      });
    }
  },

  async createCandidate(req, res) {
    try {
      // Add user IDs to candidate data
      const { file } = req;

      if (!file) {
        logger.error("File is required for candidate creation");
        return res.status(400).json({
          success: false,
          message: "File is required for candidate creation",
        });
      }

      const result = await candidateService.createCandidate(file, req.body, req.user.id);

      res.status(201).json(result);
    } catch (error) {
      logger.error("Failed to create candidate:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to create candidate",
      });
    }
  },

  async updateCandidate(req, res) {
    try {
      if (Object.keys(req.body).length === 0) {
        logger.error(messages.NO_DATA_PROVIDED_FOR_UPDATE);
        return responseHandler.error(res, messages.NO_DATA_PROVIDED_FOR_UPDATE, 400);
      }

      const result = await candidateService.updateCandidate(req.params.id, req.body);

      if (!result.success) {
        logger.info(`Failed to update candidate: ${result.message}`);
        return responseHandler.error(res, result.message, result.status || 400);
      }

      logger.info(`Candidate updated: ${req.params.id}`);
      return responseHandler.success(res, result.candidate, messages.UPDATE_CANDIDATE);
    } catch (err) {
      logger.error("Failed to update candidate:", err);
      return responseHandler.error(res, "Failed to update candidate");
    }
  },

  async deleteCandidate(req, res) {
    try {
      const result = await candidateService.deleteCandidate(req.params.id, req.user.id);

      if (!result.success) {
        logger.info(`Failed to delete candidate: ${result.message}`);
        return responseHandler.error(res, result.message, result.status || 400);
      }

      logger.info(`Candidate deleted: ${req.params.id}`);
      return responseHandler.success(res, { message: "Candidate deleted successfully" });
    } catch (err) {
      logger.error("Failed to delete candidate:", err);
      return responseHandler.error(res, "Failed to delete candidate");
    }
  },

  async postToPlatform(req, res) {
    try {
      const result = await candidateService.postToPlatform(req.params.id, req.body);

      if (!result.success) {
        logger.info(`Failed to post candidate to platform: ${result.message}`);
        return responseHandler.error(res, result.message, result.status || 400);
      }

      logger.info(`Candidate posted to platform: ${req.params.id}`);
      return responseHandler.success(res, { jobBoardPost: result.jobBoardPost });
    } catch (err) {
      logger.error("Failed to post candidate to platform:", err);
      return responseHandler.error(res, "Failed to post candidate to platform");
    }
  },

  async updateCandidateStatus(req, res) {
    try {
      const { id } = req.params;
      const { status } = req.body;

      if (!status) {
        logger.error("Candidate status is required for update");
        return responseHandler.error(res, "Candidate status is required", 400);
      }

      const result = await candidateService.updateCandidateStatus(id, status);

      if (!result.success) {
        logger.info(`Failed to update candidate status: ${result.message}`);
        return responseHandler.error(res, result.message, result.status || 400);
      }

      logger.info(`Candidate status updated: ${id}`);
      return responseHandler.success(res, result.candidate, messages.UPDATE_CANDIDATE);
    } catch (err) {
      logger.error("Failed to update candidate status:", err);
      return responseHandler.error(res, "Failed to update candidate status");
    }
  },
};
