import axios from "axios";
import { logger } from "./logger.js";

const N8N_WEBHOOK_URL = process.env.N8N_WEBHOOK_URL;

export const n8nIntegrator = {
  async workflowTrigger(workflowId, data, contentType = "json") {
    try {
      const webhookURL = `${N8N_WEBHOOK_URL}/${workflowId}`;
      const response = await axios({
        method: "post",
        url: webhookURL,
        headers:
          contentType === "json"
            ? {
                "Content-Type": "application/json",
              }
            : data.getHeaders(),
        data: data,
        timeout: 30000,
      });

      if (!response.data) {
        throw new Error("No data received from n8n workflow");
      }

      return response.data;
    } catch (error) {
      logger.error("n8n workflow error:", {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
      });
      throw error;
    }
  },
};
