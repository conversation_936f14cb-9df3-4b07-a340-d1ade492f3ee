import {
  Briefcase,
  MapPin,
  Clock,
  Calendar,
  Bookmark,
  Send,
  Share2,
  Copy,
  Users,
  DollarSign,
  IndianRupee,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { type Job, useJobs } from "@/hooks/useJobs";
import { toast } from "@/components/ui/sonner";
import { cn, formatWorkLocation } from "@/lib/utils";
import { useAuth } from "@/components/auth/AuthProvider";
import { useState, useRef } from "react";

interface JobDescriptionProps {
  job: Job;
  expanded?: boolean;
}

export function JobDescription({
  job,
  expanded = false,
  showStatus = true,
}: JobDescriptionProps & { showStatus?: boolean }) {
  const getStatusColor = (status?: string) => {
    switch (status) {
      case "published":
        return "bg-green-100 text-green-800";
      case "draft":
        return "bg-yellow-100 text-yellow-800";
      case "closed":
        return "bg-gray-100 text-gray-800";
      case "open":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-blue-100 text-blue-800";
    }
  };

  const copyJobLink = () => {
    // In a real app, this would be a real URL
    navigator.clipboard.writeText(`https://example.com/jobs/${job.id}`);
    toast.success("Job link copied to clipboard");
  };

  const shareJob = () => {
    toast.success("Share dialog would open here", {
      description: "This would integrate with social sharing in a real app",
    });
  };

  const applyForJob = () => {
    toast.success("Application form would open here", {
      description: "This would show an application form in a real app",
    });
  };

  const saveJob = () => {
    toast.success("Job saved to your bookmarks", {
      description: "You can view saved jobs in your profile",
    });
  };

  // Format salary range
  const formatSalary = () => {
    if (job.salary_min && job.salary_max) {
      return `${job.salary_min.toLocaleString()} - ${job.salary_max.toLocaleString()}`;
    } else if (job.salary_min) {
      return `${job.salary_min.toLocaleString()}+`;
    } else if (job.salary_max) {
      return `Up to ${job.salary_max.toLocaleString()}`;
    }
    return "Competitive";
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Capitalize first letter of status
  const capitalizedStatus = job.status
    ? job.status.charAt(0).toUpperCase() + job.status.slice(1)
    : "Unknown";

  const isExperienceRangeAvailable = job.min_experience && job.max_experience;

  return (
    <div className={cn("space-y-6", expanded ? "p-6" : "")}>
      <div className="flex flex-col space-y-4">
        {showStatus && (
          <div className="flex items-center justify-between">
            <Badge variant="outline" className={getStatusColor(job.status)}>
              {capitalizedStatus}
            </Badge>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={saveJob}
                className="rounded-full hover:bg-primary/10"
              >
                <Bookmark className="h-4 w-4" />
                <span className="sr-only">Save job</span>
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={shareJob}
                className="rounded-full hover:bg-primary/10"
              >
                <Share2 className="h-4 w-4" />
                <span className="sr-only">Share job</span>
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={copyJobLink}
                className="rounded-full hover:bg-primary/10"
              >
                <Copy className="h-4 w-4" />
                <span className="sr-only">Copy link</span>
              </Button>
            </div>
          </div>
        )}

        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">{job.title}</h1>
          <div className="flex items-center gap-2 pt-2">
            <Button onClick={applyForJob} className="gap-1">
              <Send className="h-4 w-4" />
              Apply Now
            </Button>
          </div>
        </div>

        <div className="flex items-center gap-4 text-sm text-muted-foreground flex-wrap">
          {job.department && (
            <div className="flex items-center">
              <Briefcase className="mr-1 h-4 w-4" />
              <span>{job.department}</span>
            </div>
          )}
          <div className="flex items-center">
            <MapPin className="mr-1 h-4 w-4" />
            <span>{formatWorkLocation(job.location)}</span>
          </div>
          <div className="flex items-center">
            <Clock className="mr-1 h-4 w-4" />
            <span className="capitalize">{job.employment_type?.replace("-", " ")}</span>
          </div>
          <div className="flex items-center">
            <DollarSign className="mr-1 h-4 w-4" />
            <span>
              {formatSalary()}
              {job.salary_frequency && (
                <span className="text-muted-foreground"> {job.salary_frequency.toLowerCase()}</span>
              )}
            </span>
          </div>
          {isExperienceRangeAvailable && (
            <div className="flex items-center">
              <Users className="mr-1 h-4 w-4" />
              <span>
                {job.min_experience}-{job.max_experience} year{job.max_experience > 0 ? "s" : ""}{" "}
                experience
              </span>
            </div>
          )}
          <div className="flex items-center">
            <Calendar className="mr-1 h-4 w-4" />
            <span>Posted {formatDate(job.created_at)}</span>
          </div>
          {/* TODO: Add applicant count when available in API */}
          <div className="flex items-center">
            <Users className="mr-1 h-4 w-4" />
            <span>0 Applicants</span>
          </div>
        </div>
      </div>

      <div className="space-y-6">
        <div>
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-lg font-semibold">Job Description</h2>
          </div>
          <p className="text-sm text-muted-foreground">{job.description}</p>
        </div>

        {job.responsibilities && job.responsibilities.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold mb-2">Responsibilities</h2>
            <ul className="list-disc pl-5 space-y-1 text-sm text-muted-foreground">
              {job.responsibilities.map((item, i) => (
                <li key={i}>{item}</li>
              ))}
            </ul>
          </div>
        )}

        {job.requirements && job.requirements.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold mb-2">Requirements</h2>
            <ul className="list-disc pl-5 space-y-1 text-sm text-muted-foreground">
              {job.requirements.map((item, i) => (
                <li key={i}>{item}</li>
              ))}
            </ul>
          </div>
        )}

        {job.skills && job.skills.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold mb-2">Skills</h2>
            <div className="flex flex-wrap gap-2">
              {job.skills.map((skill, i) => (
                <Badge key={i} variant="secondary" className="text-xs">
                  {skill}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {job.tools_and_tech && (
          <div>
            <h2 className="text-lg font-semibold mb-2">Tools & Technology</h2>
            <p className="text-sm text-muted-foreground">{job.tools_and_tech}</p>
          </div>
        )}

        {job.good_to_have && (
          <div>
            <h2 className="text-lg font-semibold mb-2">Good to Have</h2>
            <ul className="list-disc pl-5 space-y-1 text-sm text-muted-foreground">
              {job.good_to_have
                .split(",")
                .filter(item => item.trim())
                .map((item, i) => (
                  <li key={i}>{item.trim().replace(/["'()\[\]{}]/g, "")}</li>
                ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}
