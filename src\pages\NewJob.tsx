import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from '@/components/ui/card';
import { useAuth } from '@/components/auth/AuthProvider';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from '@/components/ui/sonner';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useJobs } from '@/hooks/useJobs';

// Form schema validation
const newJobSchema = z.object({
  creatorEmail: z.string().email({ message: 'Valid email required' }),
  title: z.string().min(3, { message: 'Title must be at least 3 characters' }),
  department: z.string().min(1, { message: 'Department is required' }),
  description: z.string().min(10, { message: 'Please provide more details' }),
  minExperience: z.string().min(1, { message: 'Minimum experience is required' }),
  maxExperience: z.string().min(1, { message: 'Maximum experience is required' }),
  workLocation: z.string().min(1, { message: 'Work location is required' }),
  officeLocation: z.string().optional(),
  workType: z.enum(['full-time', 'part-time', 'contract', 'internship'], { message: 'Work type is required' }),
  salaryRange: z.object({
    min: z.string().min(1, { message: 'Minimum salary is required' }),
    max: z.string().min(1, { message: 'Maximum salary is required' }),
  }),
  salaryFrequency: z.enum(['monthly', 'yearly'], { message: 'Salary frequency is required' }),
  toolsAndTech: z.string().optional(),
  goodToHaveSkills: z.string().optional(),
});

type NewJobFormValues = z.infer<typeof newJobSchema>;

export default function NewJob() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { createJob, loading } = useJobs();
  
  const form = useForm<NewJobFormValues>({
    resolver: zodResolver(newJobSchema),
    defaultValues: {
      creatorEmail: user?.email || '',
      title: '',
      department: '',
      description: '',
      minExperience: '',
      maxExperience: '',
      workLocation: '',
      officeLocation: '',
      workType: undefined,
      salaryRange: {
        min: '',
        max: '',
      },
      salaryFrequency: 'monthly',
      toolsAndTech: '',
      goodToHaveSkills: '',
    },
  });

  async function onSubmit(data: NewJobFormValues) {
    try {
      // Transform form data to match API expectations
      const jobData = {
        title: data.title,
        description: data.description,
        department: data.department,
        location: data.workLocation,
        office_location: data.officeLocation || '',
        employment_type: data.workType as 'full-time' | 'part-time' | 'contract' | 'internship',
        status: 'draft' as const,
        salary_min: parseInt(data.salaryRange.min),
        salary_max: parseInt(data.salaryRange.max),
        salary_frequency: data.salaryFrequency,
        tools_and_tech: data.toolsAndTech || '',
        good_to_have: data.goodToHaveSkills || '',
        min_experience: parseInt(data.minExperience),
        max_experience: parseInt(data.maxExperience),
        creator_email: data.creatorEmail,
      };
      const result = await createJob(jobData);
      
      if (result?.success) {
        toast.success('Job created successfully!', {
          description: `${data.title} has been posted`,
        });
        navigate('/jobs');
      } else {
        throw new Error(result?.message || 'Failed to create job');
      }
    } catch (error) {
      toast.error('Failed to create job', {
        description: error instanceof Error ? error.message : 'There was an error creating the job. Please try again.',
      });
      console.error('Error creating job:', error);
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Create New Job</h1>
          <p className="text-muted-foreground">
            Fill in the details below to create a new job posting
          </p>
        </div>
        <Button variant="outline" onClick={() => navigate('/jobs')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Jobs
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Job Details</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <FormField
                control={form.control}
                name="creatorEmail"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Creator Email</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        disabled
                        className="bg-muted cursor-not-allowed"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Job Title</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., Java Architect, Senior UX Designer"
                        className="focus-visible:ring-2 focus-visible:ring-ring transition-colors"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="department"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Department</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., Engineering, Design, HR"
                        className="focus-visible:ring-2 focus-visible:ring-ring transition-colors"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="minExperience"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Minimum Experience</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="e.g., 2"
                            className="focus-visible:ring-2 focus-visible:ring-ring transition-colors"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="maxExperience"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Maximum Experience</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="e.g., 5"
                            className="focus-visible:ring-2 focus-visible:ring-ring transition-colors"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="workLocation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Work Location</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select work location" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="on-site">On-Site</SelectItem>
                          <SelectItem value="remote">Remote</SelectItem>
                          <SelectItem value="hybrid">Hybrid</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="officeLocation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Office Location</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g., New York, San Francisco, London"
                          className="focus-visible:ring-2 focus-visible:ring-ring transition-colors"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="workType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Work Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select work type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="full-time">Full-Time</SelectItem>
                          <SelectItem value="part-time">Part-Time</SelectItem>
                          <SelectItem value="contract">Contract</SelectItem>
                          <SelectItem value="internship">Intern</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="salaryRange.min"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Minimum Salary</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="e.g., 50000"
                            className="focus-visible:ring-2 focus-visible:ring-ring transition-colors"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="salaryRange.max"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Maximum Salary</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="e.g., 80000"
                            className="focus-visible:ring-2 focus-visible:ring-ring transition-colors"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="salaryFrequency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Salary Frequency</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select frequency" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="monthly">Monthly</SelectItem>
                          <SelectItem value="yearly">Yearly</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Job Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe the job, responsibilities, and requirements..."
                        className="min-h-[120px] focus-visible:ring-2 focus-visible:ring-ring transition-colors"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="toolsAndTech"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tools and Technologies</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="List the required tools and technologies..."
                          className="min-h-[80px] focus-visible:ring-2 focus-visible:ring-ring transition-colors"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="goodToHaveSkills"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Good to Have Skills</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="List any preferred skills or qualifications..."
                          className="min-h-[80px] focus-visible:ring-2 focus-visible:ring-ring transition-colors"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate('/jobs')}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? 'Creating Job...' : 'Create Job'}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
