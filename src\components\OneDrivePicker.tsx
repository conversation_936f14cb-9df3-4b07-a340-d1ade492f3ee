import { useState, useEffect } from "react";
import { msalInstance } from "@/App";
import { loginRequest } from "@/config/microsoft.config";
import { Button } from "@/components/ui/button";
import { FolderOpen, Loader2 } from "lucide-react";

export interface OneDriveFile {
  id: string;
  name: string;
  size?: number;
  folder?: {
    childCount: number;
  };
  file?: {
    mimeType: string;
  };
  webUrl: string;
  downloadUrl?: string;
}

export interface OneDrivePickerData {
  files: OneDriveFile[];
  accessToken: string;
}

interface OneDrivePickerProps {
  onPicked: (data: OneDrivePickerData) => void;
  onError?: (error: string) => void;
  onCancel?: () => void;
  allowMultiple?: boolean;
  allowFolders?: boolean;
  disabled?: boolean;
  className?: string;
}

export function OneDrivePicker({
  onPicked,
  onError,
  onCancel,
  allowMultiple = true,
  allowFolders = true,
  disabled = false,
  className = "",
}: OneDrivePickerProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Check if user is already authenticated
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const accounts = msalInstance.getAllAccounts();
      if (accounts.length > 0) {
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error("Error checking auth status:", error);
    }
  };

  const authenticateUser = async (): Promise<string | null> => {
    try {
      setIsLoading(true);

      // Try to get token silently first
      const accounts = msalInstance.getAllAccounts();
      if (accounts.length > 0) {
        try {
          const silentRequest = {
            ...loginRequest,
            account: accounts[0],
          };
          const response = await msalInstance.acquireTokenSilent(silentRequest);
          setIsAuthenticated(true);
          return response.accessToken;
        } catch (silentError) {
          console.log("Silent token acquisition failed, falling back to popup");
        }
      }

      // If silent acquisition fails, use popup
      const response = await msalInstance.acquireTokenPopup(loginRequest);
      setIsAuthenticated(true);
      return response.accessToken;
    } catch (error: any) {
      console.error("Authentication failed:", error);
      const errorMessage = error.message || "Authentication failed";
      onError?.(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const openOneDrivePicker = async () => {
    try {
      setIsLoading(true);

      // Authenticate user first
      const accessToken = await authenticateUser();
      if (!accessToken) {
        return;
      }

      // Use Microsoft Graph API to get files from OneDrive root
      // This is a simpler approach that avoids OneDrive.js SDK issues
      const graphUrl =
        "https://graph.microsoft.com/v1.0/me/drive/root/children?$select=id,name,size,folder,file,webUrl,@microsoft.graph.downloadUrl";

      const response = await fetch(graphUrl, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch OneDrive files: ${response.statusText}`);
      }

      const data = await response.json();
      const files = data.value || [];

      // Filter files based on preferences
      let filteredFiles = files;
      if (!allowFolders) {
        filteredFiles = files.filter((file: OneDriveFile) => !file.folder);
      }

      setIsLoading(false);
      onPicked({
        files: filteredFiles,
        accessToken,
      });
    } catch (error: unknown) {
      setIsLoading(false);
      console.error("Error accessing OneDrive:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to access OneDrive";
      onError?.(errorMessage);
    }
  };

  const getButtonText = () => {
    if (isLoading) return "Loading...";
    if (!isAuthenticated) return "Connect OneDrive";
    return "Select from OneDrive";
  };

  const isButtonDisabled = disabled || isLoading;

  return (
    <Button
      type="button"
      variant="outline"
      onClick={openOneDrivePicker}
      disabled={isButtonDisabled}
      className={`w-full ${className}`}
    >
      {isLoading ? (
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
      ) : (
        <FolderOpen className="mr-2 h-4 w-4" />
      )}
      {getButtonText()}
    </Button>
  );
}
