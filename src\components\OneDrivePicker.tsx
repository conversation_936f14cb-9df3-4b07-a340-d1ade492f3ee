import { useState, useEffect } from 'react';
import { msalInstance } from '@/App';
import { loginRequest } from '@/config/microsoft.config';
import { Button } from '@/components/ui/button';
import { FolderOpen, Loader2 } from 'lucide-react';

declare global {
  interface Window {
    OneDrive: any;
  }
}

export interface OneDriveFile {
  id: string;
  name: string;
  size?: number;
  folder?: {
    childCount: number;
  };
  file?: {
    mimeType: string;
  };
  webUrl: string;
  downloadUrl?: string;
}

export interface OneDrivePickerData {
  files: OneDriveFile[];
  accessToken: string;
}

interface OneDrivePickerProps {
  onPicked: (data: OneDrivePickerData) => void;
  onError?: (error: string) => void;
  onCancel?: () => void;
  allowMultiple?: boolean;
  allowFolders?: boolean;
  disabled?: boolean;
  className?: string;
}

export function OneDrivePicker({
  onPicked,
  onError,
  onCancel,
  allowMultiple = true,
  allowFolders = true,
  disabled = false,
  className = "",
}: OneDrivePickerProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [oneDriveReady, setOneDriveReady] = useState(false);

  useEffect(() => {
    // Check if OneDrive SDK is loaded
    const checkOneDrive = () => {
      if (window.OneDrive) {
        setOneDriveReady(true);
      } else {
        setTimeout(checkOneDrive, 200);
      }
    };
    checkOneDrive();

    // Check if user is already authenticated
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const accounts = msalInstance.getAllAccounts();
      if (accounts.length > 0) {
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
    }
  };

  const authenticateUser = async (): Promise<string | null> => {
    try {
      setIsLoading(true);
      
      // Try to get token silently first
      const accounts = msalInstance.getAllAccounts();
      if (accounts.length > 0) {
        try {
          const silentRequest = {
            ...loginRequest,
            account: accounts[0],
          };
          const response = await msalInstance.acquireTokenSilent(silentRequest);
          setIsAuthenticated(true);
          return response.accessToken;
        } catch (silentError) {
          console.log('Silent token acquisition failed, falling back to popup');
        }
      }

      // If silent acquisition fails, use popup
      const response = await msalInstance.acquireTokenPopup(loginRequest);
      setIsAuthenticated(true);
      return response.accessToken;
    } catch (error: any) {
      console.error('Authentication failed:', error);
      const errorMessage = error.message || 'Authentication failed';
      onError?.(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const openOneDrivePicker = async () => {
    if (!oneDriveReady) {
      onError?.('OneDrive SDK is not loaded yet. Please try again.');
      return;
    }

    try {
      setIsLoading(true);

      // Authenticate user first
      const accessToken = await authenticateUser();
      if (!accessToken) {
        return;
      }

      // Configure OneDrive picker options
      const pickerOptions = {
        clientId: process.env.ONE_DRIVE_CLIENT_ID,
        action: 'query',
        multiSelect: allowMultiple,
        advanced: {
          filter: allowFolders ? 'folder,file' : 'file',
          queryParameters: 'select=id,name,size,folder,file,webUrl,@microsoft.graph.downloadUrl',
        },
        success: (files: OneDriveFile[]) => {
          setIsLoading(false);
          onPicked({
            files,
            accessToken,
          });
        },
        cancel: () => {
          setIsLoading(false);
          onCancel?.();
        },
        error: (error: any) => {
          setIsLoading(false);
          console.error('OneDrive picker error:', error);
          onError?.(error.message || 'Failed to open OneDrive picker');
        },
      };

      // Open the OneDrive picker
      window.OneDrive.open(pickerOptions);
    } catch (error: any) {
      setIsLoading(false);
      console.error('Error opening OneDrive picker:', error);
      onError?.(error.message || 'Failed to open OneDrive picker');
    }
  };

  const getButtonText = () => {
    if (isLoading) return 'Loading...';
    if (!oneDriveReady) return 'Loading OneDrive...';
    if (!isAuthenticated) return 'Connect OneDrive';
    return 'Select from OneDrive';
  };

  const isButtonDisabled = disabled || isLoading || !oneDriveReady;

  return (
    <Button
      type="button"
      variant="outline"
      onClick={openOneDrivePicker}
      disabled={isButtonDisabled}
      className={`w-full ${className}`}
    >
      {isLoading ? (
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
      ) : (
        <FolderOpen className="mr-2 h-4 w-4" />
      )}
      {getButtonText()}
    </Button>
  );
}
