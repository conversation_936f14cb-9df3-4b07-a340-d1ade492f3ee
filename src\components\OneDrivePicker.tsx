import { useState, useEffect } from "react";
import { msalInstance } from "@/App";
import { loginRequest } from "@/config/microsoft.config";
import { Button } from "@/components/ui/button";
import { FolderOpen, Loader2, ChevronRight, Folder, File } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";

export interface OneDriveFile {
  id: string;
  name: string;
  size?: number;
  folder?: {
    childCount: number;
  };
  file?: {
    mimeType: string;
  };
  webUrl: string;
  downloadUrl?: string;
}

export interface OneDrivePickerData {
  files: OneDriveFile[];
  accessToken: string;
}

interface OneDrivePickerProps {
  onPicked: (data: OneDrivePickerData) => void;
  onError?: (error: string) => void;
  onCancel?: () => void;
  allowMultiple?: boolean;
  allowFolders?: boolean;
  disabled?: boolean;
  className?: string;
}

export function OneDrivePicker({
  onPicked,
  onError,
  onCancel,
  allowMultiple = true,
  allowFolders = true,
  disabled = false,
  className = "",
}: OneDrivePickerProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [showPicker, setShowPicker] = useState(false);
  const [currentFolder, setCurrentFolder] = useState<OneDriveFile | null>(null);
  const [folderContents, setFolderContents] = useState<OneDriveFile[]>([]);
  const [breadcrumbs, setBreadcrumbs] = useState<OneDriveFile[]>([]);
  const [selectedFolder, setSelectedFolder] = useState<OneDriveFile | null>(null);

  useEffect(() => {
    // Check if user is already authenticated
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const accounts = msalInstance.getAllAccounts();
      if (accounts.length > 0) {
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error("Error checking auth status:", error);
    }
  };

  const authenticateUser = async (): Promise<string | null> => {
    try {
      setIsLoading(true);

      // Try to get token silently first
      const accounts = msalInstance.getAllAccounts();
      if (accounts.length > 0) {
        try {
          const silentRequest = {
            ...loginRequest,
            account: accounts[0],
          };
          const response = await msalInstance.acquireTokenSilent(silentRequest);
          setIsAuthenticated(true);
          return response.accessToken;
        } catch (silentError) {
          console.log("Silent token acquisition failed, falling back to popup");
        }
      }

      // If silent acquisition fails, use popup
      const response = await msalInstance.acquireTokenPopup(loginRequest);
      setIsAuthenticated(true);
      return response.accessToken;
    } catch (error: any) {
      console.error("Authentication failed:", error);
      const errorMessage = error.message || "Authentication failed";
      onError?.(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const openOneDrivePicker = async () => {
    try {
      setIsLoading(true);

      // Authenticate user first
      const accessToken = await authenticateUser();
      if (!accessToken) {
        return;
      }

      // Load root folder contents
      await loadFolderContents(null, accessToken);
      setShowPicker(true);
      setIsLoading(false);
    } catch (error: unknown) {
      setIsLoading(false);
      console.error("Error opening OneDrive picker:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to open OneDrive picker";
      onError?.(errorMessage);
    }
  };

  const loadFolderContents = async (folder: OneDriveFile | null, accessToken: string) => {
    try {
      const folderId = folder?.id || "root";
      const graphUrl = `https://graph.microsoft.com/v1.0/me/drive/items/${folderId}/children?$select=id,name,size,folder,file,webUrl,@microsoft.graph.downloadUrl`;

      const response = await fetch(graphUrl, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch folder contents: ${response.statusText}`);
      }

      const data = await response.json();
      const items = data.value || [];

      setFolderContents(items);
      setCurrentFolder(folder);
    } catch (error: unknown) {
      console.error("Error loading folder contents:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to load folder contents";
      onError?.(errorMessage);
    }
  };

  const navigateToFolder = async (folder: OneDriveFile) => {
    const accessToken = await authenticateUser();
    if (!accessToken) return;

    // Add current folder to breadcrumbs
    if (currentFolder) {
      setBreadcrumbs(prev => [...prev, currentFolder]);
    }

    await loadFolderContents(folder, accessToken);
  };

  const navigateUp = async () => {
    const accessToken = await authenticateUser();
    if (!accessToken) return;

    if (breadcrumbs.length > 0) {
      const parentFolder = breadcrumbs[breadcrumbs.length - 1];
      setBreadcrumbs(prev => prev.slice(0, -1));
      await loadFolderContents(parentFolder, accessToken);
    } else {
      // Go to root
      await loadFolderContents(null, accessToken);
    }
  };

  const selectFolder = () => {
    if (!selectedFolder) {
      onError?.("Please select a folder first");
      return;
    }

    console.log("Selected folder:", selectedFolder);

    // Get files in the selected folder
    const accessToken = authenticateUser();
    accessToken.then(token => {
      if (token) {
        // Fetch files in the selected folder
        const folderId = selectedFolder.id;
        const graphUrl = `https://graph.microsoft.com/v1.0/me/drive/items/${folderId}/children?$select=id,name,size,folder,file,webUrl,@microsoft.graph.downloadUrl`;

        fetch(graphUrl, {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        })
          .then(response => response.json())
          .then(data => {
            const files = data.value || [];
            console.log("Files in selected folder:", files);

            onPicked({
              files,
              accessToken: token,
            });

            setShowPicker(false);
            setSelectedFolder(null);
          })
          .catch(error => {
            console.error("Error fetching folder files:", error);
            onError?.("Failed to fetch files from selected folder");
          });
      }
    });
  };

  const getButtonText = () => {
    if (isLoading) return "Loading...";
    if (!isAuthenticated) return "Connect OneDrive";
    return "Select from OneDrive";
  };

  const isButtonDisabled = disabled || isLoading;

  return (
    <>
      <Button
        type="button"
        variant="outline"
        onClick={openOneDrivePicker}
        disabled={isButtonDisabled}
        className={`w-full ${className}`}
      >
        {isLoading ? (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        ) : (
          <FolderOpen className="mr-2 h-4 w-4" />
        )}
        {getButtonText()}
      </Button>

      <Dialog open={showPicker} onOpenChange={setShowPicker}>
        <DialogContent className="max-w-2xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>Select OneDrive Folder</DialogTitle>
          </DialogHeader>

          <div className="flex flex-col space-y-4">
            {/* Breadcrumbs */}
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Button
                variant="ghost"
                size="sm"
                onClick={navigateUp}
                disabled={breadcrumbs.length === 0}
              >
                OneDrive
              </Button>
              {breadcrumbs.map((folder, index) => (
                <div key={folder.id} className="flex items-center space-x-2">
                  <ChevronRight className="h-4 w-4" />
                  <span>{folder.name}</span>
                </div>
              ))}
              {currentFolder && (
                <div className="flex items-center space-x-2">
                  <ChevronRight className="h-4 w-4" />
                  <span className="font-medium">{currentFolder.name}</span>
                </div>
              )}
            </div>

            {/* Folder Contents */}
            <div className="border rounded-lg max-h-96 overflow-y-auto">
              {folderContents.length === 0 ? (
                <div className="p-8 text-center text-muted-foreground">This folder is empty</div>
              ) : (
                <div className="space-y-1 p-2">
                  {folderContents
                    .filter(item => item.folder) // Only show folders
                    .map(folder => (
                      <div
                        key={folder.id}
                        className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer hover:bg-muted ${
                          selectedFolder?.id === folder.id
                            ? "bg-primary/10 border border-primary"
                            : ""
                        }`}
                        onClick={() => setSelectedFolder(folder)}
                        onDoubleClick={() => navigateToFolder(folder)}
                      >
                        <Folder className="h-5 w-5 text-blue-500" />
                        <div className="flex-1">
                          <div className="font-medium">{folder.name}</div>
                          {folder.folder && (
                            <div className="text-sm text-muted-foreground">
                              {folder.folder.childCount} items
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </div>

            {/* Selected folder info */}
            {selectedFolder && (
              <div className="p-3 bg-muted rounded-lg">
                <div className="font-medium">Selected: {selectedFolder.name}</div>
                <div className="text-sm text-muted-foreground">
                  {selectedFolder.folder?.childCount || 0} items in this folder
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowPicker(false);
                setSelectedFolder(null);
                onCancel?.();
              }}
            >
              Cancel
            </Button>
            <Button onClick={selectFolder} disabled={!selectedFolder}>
              Select Folder
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
