import { useState } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { FormModal } from "@/components/common/FormModal";
import { useJobs } from "@/hooks/useJobs";
import { BULK_UPLOAD_CANDIDATES_CONSTANTS } from "@/utils/Constant";
import { toast } from "@/components/ui/sonner";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { OneDrivePicker, OneDrivePickerData } from "@/components/OneDrivePicker.tsx";
import { useCandidates } from "@/hooks/useCandidates";

const { VALIDATION, LABELS, PLACEHOLDERS, MODAL, TOAST } = BULK_UPLOAD_CANDIDATES_CONSTANTS;

type BulkUploadFormValues = {
  jobId: string;
  source: string;
  folder: FileList | null;
  oneDriveFolder: {
    files: Array<{
      id: string;
      name: string;
      size?: number;
      webUrl: string;
    }>;
    accessToken: string;
    selectedFolder: {
      id: string;
      name: string;
      webUrl: string;
    };
  } | null;
};

export function BulkUploadCandidatesModal() {
  const { jobs, loading: jobsLoading } = useJobs();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const { bulkUploadCandidates } = useCandidates();

  const form = useForm<BulkUploadFormValues>({
    defaultValues: {
      jobId: "",
      source: "",
      folder: null,
      oneDriveFolder: null,
    },
  });

  const { control, handleSubmit, reset } = form;

  const onSubmit = async (data: BulkUploadFormValues) => {
    if (!data.jobId) {
      form.setError("jobId", { message: VALIDATION.JOB_REQUIRED });
      return;
    }

    const hasOneDriveFiles = data.oneDriveFolder?.files?.length > 0;
    if (!hasOneDriveFiles) {
      form.setError("folder", { message: VALIDATION.FOLDER_REQUIRED });
      return;
    }
    setIsSubmitting(true);

    const payload = {
      job_id: data.jobId,
      source: data.source,
      oneDriveFolderId: data.oneDriveFolder.selectedFolder.id,
      can_status: "applied",
    };
    const fileCount = data.oneDriveFolder.files.length;

    // await bulkUploadCandidates(payload);
    setIsSubmitting(false);
    toast.success(TOAST.SUCCESS.TITLE, { description: TOAST.SUCCESS.DESCRIPTION(fileCount) });
    reset();
  };
  return (
    <FormModal
      triggerText={MODAL.DEFAULT_BUTTON_TEXT}
      title={MODAL.TITLE}
      description={MODAL.DESCRIPTION}
      onSubmit={handleSubmit(onSubmit)}
      submitText={MODAL.SUBMIT_TEXT}
      isSubmitting={isSubmitting || jobsLoading}
      initialOpen={modalOpen}
      onOpenChange={setModalOpen}
    >
      <Form {...form}>
        <div className="space-y-6">
          <FormField
            control={control}
            name="jobId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{LABELS.JOB_MAPPING}</FormLabel>
                <Controller
                  control={control}
                  name="jobId"
                  render={({ field }) => (
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={jobsLoading || jobs.length === 0}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue
                            placeholder={
                              jobsLoading
                                ? PLACEHOLDERS.LOADING_JOBS
                                : jobs.length === 0
                                ? PLACEHOLDERS.NO_JOBS
                                : PLACEHOLDERS.SELECT_JOB
                            }
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {jobs.map(job => (
                          <SelectItem key={job.id} value={job.id}>
                            {job.title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Source Field */}
          <FormField
            control={control}
            name="source"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Source</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter candidate source (e.g. LinkedIn, Referral)"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="oneDriveFolder"
            render={({ field: { value, onChange, ...fieldProps } }) => (
              <FormItem>
                <FormLabel>{LABELS.ONEDRIVE_FOLDER_SELECT}</FormLabel>
                <FormControl>
                  <OneDrivePicker
                    onPicked={(data: OneDrivePickerData) => {
                      onChange(data);
                    }}
                    onError={(error: string) => {
                      toast.error(VALIDATION.ONEDRIVE_AUTH_FAILED, {
                        description: error,
                      });
                    }}
                    onCancel={() => {
                      // Handle cancel if needed
                    }}
                    allowMultiple={true}
                    allowFolders={true}
                    className="w-full"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground mt-1">
                  {PLACEHOLDERS.SELECT_ONEDRIVE_FOLDER}
                </div>
                <FormMessage />
                {value && value.files && value.files.length > 0 && (
                  <div className="text-xs mt-2 p-2 bg-green-50 border border-green-200 rounded">
                    ✓ {value.files.length} files selected from OneDrive folder:{" "}
                    <strong>{value.selectedFolder.name}</strong>
                  </div>
                )}
              </FormItem>
            )}
          />
        </div>
      </Form>
    </FormModal>
  );
}
