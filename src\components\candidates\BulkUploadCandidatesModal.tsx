import { useState } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { FormModal } from "@/components/common/FormModal";
import { useJobs } from "@/hooks/useJobs";
import { BULK_UPLOAD_CANDIDATES_CONSTANTS } from "@/utils/Constant";
import { toast } from "@/components/ui/sonner";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { GoogleDrivePicker } from "@/components/GoogleDrivePicker";
import { OneDrivePicker, OneDrivePickerData } from "@/components/OneDrivePicker";
import { useCandidates } from "@/hooks/useCandidates";

const { VALIDATION, LABELS, PLACEHOLDERS, MODAL, TOAST } = BULK_UPLOAD_CANDIDATES_CONSTANTS;

type BulkUploadFormValues = {
  jobId: string;
  folder: FileList | null;
  oneDriveFolder: {
    files: Array<{
      id: string;
      name: string;
      size?: number;
      webUrl: string;
    }>;
    accessToken: string;
  } | null;
};

export function BulkUploadCandidatesModal() {
  const { jobs, loading: jobsLoading } = useJobs();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { bulkUploadCandidates } = useCandidates();

  const form = useForm<BulkUploadFormValues>({
    defaultValues: {
      jobId: "",
      folder: null,
      oneDriveFolder: null,
    },
  });

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
    watch,
  } = form;

  const onSubmit = async (data: BulkUploadFormValues) => {
    if (!data.jobId) {
      form.setError("jobId", { message: VALIDATION.JOB_REQUIRED });
      return;
    }

    // Check if either local folder or OneDrive folder is selected
    const hasLocalFiles = data.folder && data.folder.length > 0;
    const hasOneDriveFiles =
      data.oneDriveFolder && data.oneDriveFolder.files && data.oneDriveFolder.files.length > 0;

    if (!hasLocalFiles && !hasOneDriveFiles) {
      form.setError("folder", { message: VALIDATION.FOLDER_REQUIRED });
      return;
    }

    setIsSubmitting(true);

    let payload: any;
    let fileCount = 0;

    if (hasLocalFiles) {
      payload = {
        job_id: data.jobId,
        files: data.folder,
      };
      fileCount = data.folder!.length;
    } else if (hasOneDriveFiles) {
      // For OneDrive files, we'll need to handle them differently
      // This might require additional backend support for OneDrive file processing
      payload = {
        job_id: data.jobId,
        oneDriveFiles: data.oneDriveFolder!.files,
        accessToken: data.oneDriveFolder!.accessToken,
      };
      fileCount = data.oneDriveFolder!.files.length;
    }

    await bulkUploadCandidates(payload);
    setIsSubmitting(false);
    toast.success(TOAST.SUCCESS.TITLE, {
      description: TOAST.SUCCESS.DESCRIPTION(fileCount),
    });
    reset();
  };

  return (
    <FormModal
      triggerText={MODAL.DEFAULT_BUTTON_TEXT}
      title={MODAL.TITLE}
      description={MODAL.DESCRIPTION}
      onSubmit={handleSubmit(onSubmit)}
      submitText={MODAL.SUBMIT_TEXT}
      isSubmitting={isSubmitting || jobsLoading}
    >
      <Form {...form}>
        <div className="space-y-6">
          <FormField
            control={control}
            name="jobId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{LABELS.JOB_MAPPING}</FormLabel>
                <Controller
                  control={control}
                  name="jobId"
                  render={({ field }) => (
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={jobsLoading || jobs.length === 0}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue
                            placeholder={
                              jobsLoading
                                ? PLACEHOLDERS.LOADING_JOBS
                                : jobs.length === 0
                                ? PLACEHOLDERS.NO_JOBS
                                : PLACEHOLDERS.SELECT_JOB
                            }
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {jobs.map(job => (
                          <SelectItem key={job.id} value={job.id}>
                            {job.title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="folder"
            render={({ field: { value, onChange, ...fieldProps } }) => (
              <FormItem>
                <FormLabel>{LABELS.FOLDER_SELECT}</FormLabel>
                <FormControl>
                  <Input
                    type="file"
                    multiple
                    webkitdirectory=""
                    directory=""
                    onChange={e => {
                      onChange(e.target.files);
                    }}
                    className="cursor-pointer"
                    {...fieldProps}
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground mt-1">
                  {PLACEHOLDERS.SELECT_FOLDER}
                </div>
                <FormMessage />
                {value && value.length > 0 && (
                  <div className="text-xs mt-2">{value.length} files selected</div>
                )}
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="oneDriveFolder"
            render={({ field: { value, onChange, ...fieldProps } }) => (
              <FormItem>
                <FormLabel>{LABELS.ONEDRIVE_FOLDER_SELECT}</FormLabel>
                <FormControl>
                  <OneDrivePicker
                    onPicked={(data: OneDrivePickerData) => {
                      onChange(data);
                    }}
                    onError={(error: string) => {
                      toast.error(VALIDATION.ONEDRIVE_AUTH_FAILED, {
                        description: error,
                      });
                    }}
                    onCancel={() => {
                      // Handle cancel if needed
                    }}
                    allowMultiple={true}
                    allowFolders={true}
                    className="w-full"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground mt-1">
                  {PLACEHOLDERS.SELECT_ONEDRIVE_FOLDER}
                </div>
                <FormMessage />
                {value && value.files && value.files.length > 0 && (
                  <div className="text-xs mt-2">
                    {value.files.length} files selected from OneDrive
                  </div>
                )}
              </FormItem>
            )}
          />
        </div>
      </Form>
    </FormModal>
  );
}
