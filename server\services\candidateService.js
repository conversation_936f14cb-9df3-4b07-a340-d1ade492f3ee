import { supabase } from "../config/db.js";
import { logger } from "../utils/logger.js";
import { n8nIntegrator } from "../utils/n8nIntegrator.js";
import FormData from "form-data";
import { fileUploader } from "../utils/fileUploader.js";

const { N8N_CANDIDATE_SCREENING_ID, N8N_BULK_CANDIDATE_SCREENING_ID } = process.env;

async function attachLatestInterviewStage(candidates = []) {
  if (!Array.isArray(candidates) || candidates.length === 0) return candidates;

  const candidateIds = candidates.map(c => c.id);

  // Fetch interviews by candidate_id
  const { data: interviews, error } = await supabase
    .from("interviews")
    .select("candidate_id, stage, created_at")
    .in("candidate_id", candidateIds)
    .eq("flag_deleted", false);

  if (error) {
    logger.error("Failed to fetch interviews:", error);
    throw error;
  }

  // Get latest interview per candidate
  const latestStageMap = {};

  interviews.forEach(interview => {
    const cId = interview.candidate_id;
    const existing = latestStageMap[cId];

    if (!existing || new Date(interview.created_at) > new Date(existing.created_at)) {
      latestStageMap[cId] = {
        stage: interview.stage,
        created_at: interview.created_at,
      };
    }
  });

  // Merge into candidates
  return candidates.map(candidate => ({
    ...candidate,
    latest_stage: latestStageMap[candidate.id]?.stage || null,
    stage_updated_at: latestStageMap[candidate.id]?.created_at || null,
  }));
}

export const candidateService = {
  async bulkUploadCandidates(payload) {
    try {
      // If not already FormData, convert to FormData
      let formData;
      if (payload instanceof FormData) {
        formData = payload;
      } else {
        formData = new FormData();
        for (const key in payload) {
          if (Array.isArray(payload[key])) {
            // For arrays, append each item
            payload[key].forEach((item, idx) => {
              formData.append(`${key}[${idx}]`, item);
            });
          } else {
            formData.append(key, payload[key]);
          }
        }
      }
      console.log("service", formData);
      const n8nResponse = await n8nIntegrator.workflowTrigger(
        N8N_BULK_CANDIDATE_SCREENING_ID,
        formData,
        "form-data"
      );

      return {
        message: "All candidates uploaded successfully",
        success: true,
        data: n8nResponse,
      };
    } catch (error) {
      logger.error("Failed to bulk upload candidates:", error);
      return { success: false, message: error.message || "Failed to bulk upload candidates" };
    }
  },

  async getCandidates(page = 1, limit = 10, filters) {
    try {
      const start = (page - 1) * limit;
      const end = start + limit - 1;

      let query = supabase
        .from("candidates")
        .select(
          `*,
        candidate_score: candidate_scoring!candidate_id(id, score, location, skills, education), 
        job_post:job_posts!job_id(id, title, department),
        created_by:users!created_by(id, full_name, email)`,
          { count: "exact" }
        )
        .eq("flag_deleted", false)
        .order("created_at", { ascending: false })
        .range(start, end);

      Object.entries(filters || {}).forEach(([key, value]) => {
        query = query.eq(key, value);
      });

      const { data: candidates, error, count } = await query;
      if (error) throw error;

      const enrichedCandidates = await attachLatestInterviewStage(candidates);

      return {
        candidates: enrichedCandidates,
        pagination: {
          total: count,
          page: parseInt(page),
          pages: Math.ceil(count / limit),
        },
      };
    } catch (error) {
      logger.error("Failed to get candidates:", error);
      throw error;
    }
  },

  async getCandidateById(id) {
    try {
      const { data: candidate, error } = await supabase
        .from("candidates")
        .select(
          `
        *,
        candidate_score: candidate_scoring!candidate_id(id, score,suitability_summary, unsuitability_summary, location, skills, education), 
        job_post:job_posts!job_id(id, title, department),
        created_by:users!created_by(full_name, email)
      `
        )
        .eq("id", id)
        .eq("flag_deleted", false)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          return { success: false, message: "Candidate not found" };
        }
        throw error;
      }

      const [enriched] = await attachLatestInterviewStage([candidate]);

      return { success: true, candidate: enriched };
    } catch (error) {
      logger.error("Failed to get candidate by id:", error);
      throw error;
    }
  },

  async createCandidate(file, candidateData, userId) {
    try {
      // Upload file to supabase
      const fileUpload = await fileUploader.uploadToSupabase(file, "candidates_cvs");

      // Prepare form data for n8n workflow
      const formData = new FormData();
      formData.append("cv", file.buffer, file.originalname);
      formData.append("phoneNumber", String(candidateData.phoneNumber));
      formData.append("jobId", String(candidateData.jobId));
      formData.append("name", String(candidateData.name));
      formData.append("url", String(fileUpload.url));
      formData.append("email", String(candidateData.email));
      formData.append("source", String(candidateData.source));
      formData.append("created_by", String(userId));

      // Call n8n workflow which will handle database insertion directly
      const n8nResponse = await n8nIntegrator.workflowTrigger(
        N8N_CANDIDATE_SCREENING_ID,
        formData,
        "form-data"
      );

      // Return the n8n response data
      return {
        success: true,
        message: "Candidate created successfully via n8n workflow",
        n8nResponse: n8nResponse,
      };
    } catch (error) {
      logger.error("Failed to create candidate:", error);
      throw error;
    }
  },

  async updateCandidate(id, updateData) {
    try {
      const { data: candidate, error } = await supabase
        .from("candidates")
        .update({
          ...updateData,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id)
        .eq("flag_deleted", false)
        .select(
          `
          *,
          creator:users!created_by(full_name, email)
        `
        )
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          return { success: false, message: "Candidate not found", status: 404 };
        }
        throw error;
      }

      return { success: true, candidate };
    } catch (error) {
      logger.error("Failed to update candidate:", error);
      throw error;
    }
  },

  async updateCandidateStatus(id, status) {
    try {
      const { data: candidate, error } = await supabase
        .from("candidates")
        .update({
          can_status: status,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id)
        .eq("flag_deleted", false)
        .select()
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          return { success: false, message: "Candidate not found", status: 404 };
        }
        throw error;
      }

      return { success: true, candidate };
    } catch (error) {
      logger.error("Failed to update candidate status:", error);
      throw error;
    }
  },

  async deleteCandidate(id, userId) {
    try {
      const { data: candidate, error } = await supabase
        .from("candidates")
        .update({
          flag_deleted: true,
          deleted_by: userId,
          deleted_at: new Date().toISOString(),
        })
        .eq("id", id)
        .eq("flag_deleted", false)
        .select()
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          return { success: false, message: "Candidate not found", status: 404 };
        }
        throw error;
      }

      return { success: true };
    } catch (error) {
      logger.error("Failed to delete candidate:", error);
      throw error;
    }
  },

  async postToPlatform(id, platformData) {
    try {
      const { data: candidate, error: fetchError } = await supabase
        .from("candidates")
        .select("*")
        .eq("id", id)
        .eq("flag_deleted", false)
        .single();

      if (fetchError) {
        if (fetchError.code === "PGRST116") {
          return { success: false, message: "Candidate not found", status: 404 };
        }
        throw fetchError;
      }

      // Integrate with external job boards (LinkedIn, Indeed, etc.)
      const platforms = platformData.platforms || ["linkedin", "indeed"];
      const postResults = await Promise.all(
        platforms.map(platform =>
          aiIntegrator.postCandidateToPlatform({
            platform,
            candidate,
          })
        )
      );

      // Create job board posts in Supabase
      const { error: insertError } = await supabase.from("job_board_posts").insert(
        postResults.map(result => ({
          job_post_id: id,
          platform: result.platform,
          external_url: result.post_url,
          posted_at: new Date().toISOString(),
          flag_active: true,
          flag_deleted: false,
        }))
      );

      if (insertError) throw insertError;

      // Update job status
      const { error: updateError } = await supabase
        .from("job_posts")
        .update({
          status: "published",
          updated_at: new Date().toISOString(),
        })
        .eq("id", id);

      if (updateError) throw updateError;

      return { success: true, jobBoardPost: postResults };
    } catch (error) {
      logger.error("Failed to post job to platform:", error);
      throw error;
    }
  },
};
