import { Configuration, PopupRequest } from "@azure/msal-browser";

const clientId = import.meta.env.VITE_ONE_DRIVE_CLIENT_ID;
const tenantId = import.meta.env.VITE_ONE_DRIVE_TENANT_ID;
const redirectUri = import.meta.env.VITE_ONE_DRIVE_REDIRECT_URI;

export const msalConfig: Configuration = {
  auth: {
    clientId: clientId,
    authority: `https://login.microsoftonline.com/${tenantId}`,
    redirectUri: redirectUri,
  },
  cache: {
    cacheLocation: "localStorage",
    storeAuthStateInCookie: false,
  },
};

export const loginRequest: PopupRequest = {
  scopes: ["User.Read", "Files.ReadWrite", "offline_access"],
};
