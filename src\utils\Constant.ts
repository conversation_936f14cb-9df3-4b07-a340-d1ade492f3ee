import { CandidateStatus } from "@/hooks/useCandidates";

// API Configuration
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE,
  ENDPOINTS: {
    AUTH: {
      LOGIN: "/auth/login",
      REGISTER: "/auth/register",
    },
    CANDIDATES: "/candidates",
    JOBS: "/jobs",
    INTERVIEWS: "/interviews",
    FEEDBACK: "/feedback",
    INTERVIEWERS: "/interviewers",
    GOOGLE: {
      AUTH: "/google/auth",
      CALLBACK: "/google/callback",
    },
  },
} as const;

// AddCandidateModal Constants
export const ADD_CANDIDATE_CONSTANTS = {
  // Form validation messages
  VALIDATION: {
    NAME_MIN_LENGTH: "Name must be at least 2 characters",
    EMAIL_REQUIRED: "Valid email required",
    PHONE_INVALID: "Please enter a valid phone number",
    JOB_REQUIRED: "Please select a position",
    RESUME_REQUIRED: "Resume is required",
    FILE_SIZE_LIMIT: "File size must be less than 2MB",
    FILE_TYPE_RESTRICTION: "Only PDF and DOCX files are accepted",
    SOURCE_REQUIRED: "Source is required",
  },

  // Form labels
  LABELS: {
    FULL_NAME: "Full Name *",
    EMAIL_ADDRESS: "Email Address *",
    PHONE_NUMBER: "Phone Number",
    POSITION: "Position Applying For *",
    SOURCE: "Source *",
    RESUME_UPLOAD: "Resume Upload *",
  },

  // Placeholders
  PLACEHOLDERS: {
    FULL_NAME: "Enter candidate's full name",
    EMAIL: "<EMAIL>",
    PHONE: "******-456-7890",
    SOURCE: "Enter source (e.g. LinkedIn, Indeed, Referral, etc.)",
    SELECT_POSITION: "Select a position",
    LOADING_POSITIONS: "Loading positions...",
    NO_POSITIONS: "No positions available",
  },

  // File upload
  FILE_UPLOAD: {
    ACCEPTED_FORMATS: "Accepted formats: PDF, DOCX. Max size: 2MB",
    MAX_SIZE: 2 * 1024 * 1024, // 2MB
    ACCEPTED_TYPES: [
      "application/pdf",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ] as string[],
  },

  // Modal content
  MODAL: {
    TITLE: "Add New Candidate",
    DESCRIPTION: "Add a candidate for screening. All fields marked with * are required.",
    SUBMIT_TEXT: "Add Candidate",
    DEFAULT_BUTTON_TEXT: "Add Candidate",
  },

  // Toast messages
  TOAST: {
    SUCCESS: {
      TITLE: "Candidate added successfully!",
      DESCRIPTION: (name: string) => `${name} has been added for screening`,
    },
    ERROR: {
      TITLE: "Failed to add candidate",
      DESCRIPTION: "There was an error adding the candidate. Please try again.",
    },
  },
} as const;

// useCandidates Constants
export const USE_CANDIDATES_CONSTANTS = {
  // Error messages
  ERRORS: {
    AUTH_TOKEN_NOT_FOUND: "Authentication token not found.",
    FAILED_TO_ADD_CANDIDATE: "Failed to add candidate",
    FAILED_TO_FETCH_CANDIDATES: "Failed to fetch candidates",
    FAILED_TO_FETCH_ALL_CANDIDATES: "Failed to fetch all candidates",
    FAILED_TO_FETCH_CANDIDATE: "Failed to fetch candidate",
    FAILED_TO_UPDATE_CANDIDATE_STATUS: "Failed to update candidate status",
  },

  // Success messages
  SUCCESS: {
    CANDIDATE_ADDED: "Candidate added successfully!",
    FETCHED_ALL_CANDIDATES: "Fetched all candidates successfully!",
    FETCHED_CANDIDATE: "Fetched candidate successfully!",
  },

  // Toast messages
  TOAST: {
    AUTH_ERROR: "Authentication token not found.",
    ADD_SUCCESS: "Candidate added successfully!",
    ADD_ERROR: "Failed to add candidate",
    FETCH_ERROR: "Failed to fetch candidates",
    FETCH_ALL_SUCCESS: "Fetched all candidates successfully!",
    FETCH_ALL_ERROR: "Failed to fetch all candidates",
    FETCH_CANDIDATE_SUCCESS: "Fetched candidate successfully!",
    FETCH_CANDIDATE_ERROR: "Failed to fetch candidate",
    ACCEPT_SUCCESS: "Candidate accepted successfully!",
    ACCEPT_ERROR: "Failed to accept candidate",
    DECLINE_SUCCESS: "Candidate declined successfully!",
    DECLINE_ERROR: "Failed to decline candidate",
    UPDATE_STATUS_SUCCESS: "Candidate status updated successfully!",
    UPDATE_STATUS_ERROR: "Failed to update candidate status",
  },

  // Form field mappings
  FORM_FIELDS: {
    PHONE_NUMBER: "phoneNumber",
    CV: "cv",
  },

  // Data mappings
  DATA_MAPPINGS: {
    STATUS: "can_status",
    APPLIED_DATE: "created_at",
  },
} as const;

// BulkUploadCandidatesModal Constants
export const BULK_UPLOAD_CANDIDATES_CONSTANTS = {
  VALIDATION: {
    FOLDER_REQUIRED: "Please select a folder containing candidate resumes",
    JOB_REQUIRED: "Please select a job for mapping",
    INVALID_FILE_TYPE: "Only PDF and DOCX files are accepted",
    FILE_SIZE_LIMIT: "Each file must be less than 2MB",
    ONEDRIVE_FOLDER_REQUIRED: "Please select a folder from OneDrive",
    ONEDRIVE_AUTH_FAILED: "OneDrive authentication failed. Please try again.",
  },
  LABELS: {
    JOB_MAPPING: "Map to Job *",
    FOLDER_SELECT: "Select Folder *",
    ONEDRIVE_FOLDER_SELECT: "OneDrive Folder *",
  },
  PLACEHOLDERS: {
    SELECT_JOB: "Select a job position",
    SELECT_FOLDER: "Choose a folder from your drive",
    SELECT_ONEDRIVE_FOLDER: "Choose a folder from OneDrive",
    LOADING_JOBS: "Loading jobs...",
    NO_JOBS: "No jobs available",
  },
  MODAL: {
    TITLE: "Bulk Upload Candidates",
    DESCRIPTION:
      "Upload multiple candidate resumes at once. All files in the selected folder will be processed.",
    SUBMIT_TEXT: "Upload Candidates",
    DEFAULT_BUTTON_TEXT: "Bulk Upload",
  },
  TOAST: {
    SUCCESS: {
      TITLE: "Bulk upload successful!",
      DESCRIPTION: (count: number) =>
        `Upload process for ${count} candidates has started successfully. You will receive a confirmation email once it is complete.`,
    },
    ERROR: {
      TITLE: "Bulk upload failed",
      DESCRIPTION: "There was an error uploading candidates. Please try again.",
    },
  },
};
